import { describe, it, expect } from 'vitest';
import { encryptPassword, decryptPassword, hashPassword, verifyPassword, validatePasswordStrength } from '../crypto.js';

describe('Crypto Integration Tests', () => {
  describe('Password Encryption Flow', () => {
    it('should encrypt passwords with AES-GCM format', async () => {
      const password = 'TestPassword123!';

      // Encrypt the same password multiple times
      const encrypted1 = await encryptPassword(password);
      const encrypted2 = await encryptPassword(password);

      // Should produce different encrypted values due to random IV
      expect(encrypted1).not.toBe(encrypted2);
      expect(typeof encrypted1).toBe('string');
      expect(typeof encrypted2).toBe('string');
      expect(encrypted1).toContain(':'); // Should have IV:ciphertext format
      expect(encrypted2).toContain(':'); // Should have IV:ciphertext format
      expect(encrypted1.length).toBeGreaterThan(0);
      expect(encrypted2.length).toBeGreaterThan(0);
    });

    it('should encrypt and decrypt passwords correctly', async () => {
      const password = 'TestPassword123!';

      // Encrypt the password
      const encrypted = await encryptPassword(password);

      // Decrypt the password
      const decrypted = await decryptPassword(encrypted);

      expect(decrypted).toBe(password);
    });

    it('should handle different password lengths', async () => {
      const shortPassword = 'Short1!';
      const longPassword = 'ThisIsAVeryLongPasswordWithManyCharacters123!@#$%^&*()';

      const encryptedShort = await encryptPassword(shortPassword);
      const encryptedLong = await encryptPassword(longPassword);

      expect(typeof encryptedShort).toBe('string');
      expect(typeof encryptedLong).toBe('string');
      expect(encryptedShort).toContain(':');
      expect(encryptedLong).toContain(':');
      expect(encryptedShort).not.toBe(encryptedLong);

      // Verify decryption works
      const decryptedShort = await decryptPassword(encryptedShort);
      const decryptedLong = await decryptPassword(encryptedLong);
      expect(decryptedShort).toBe(shortPassword);
      expect(decryptedLong).toBe(longPassword);
    });

    it('should handle special characters in passwords', async () => {
      const specialPassword = '!@#$%^&*()_+-=[]{}|;:,.<>?`~';

      const encrypted = await encryptPassword(specialPassword);
      const decrypted = await decryptPassword(encrypted);

      expect(typeof encrypted).toBe('string');
      expect(encrypted).toContain(':');
      expect(encrypted.length).toBeGreaterThan(0);
      expect(decrypted).toBe(specialPassword);
    });

    it('should work with custom encryption keys', async () => {
      const password = 'TestPassword123!';
      const customKey = 'mycustomkey123';

      const encrypted = await encryptPassword(password, customKey);
      const decrypted = await decryptPassword(encrypted, customKey);

      expect(encrypted).toContain(':');
      expect(decrypted).toBe(password);
    });
  });

  describe('Password Hashing and Verification', () => {
    it('should hash and verify passwords correctly', async () => {
      const password = 'TestPassword123!';

      // Hash the password
      const { hash, salt } = await hashPassword(password);

      // Verify with correct password
      const isValidCorrect = await verifyPassword(password, hash, salt);
      expect(isValidCorrect).toBe(true);

      // Verify with incorrect password
      const isValidIncorrect = await verifyPassword('WrongPassword123!', hash, salt);
      expect(isValidIncorrect).toBe(false);
    });

    it('should generate different hashes for same password with different salts', async () => {
      const password = 'TestPassword123!';

      const result1 = await hashPassword(password);
      const result2 = await hashPassword(password);

      // Different salts should produce different hashes
      expect(result1.salt).not.toBe(result2.salt);
      expect(result1.hash).not.toBe(result2.hash);
    });

    it('should produce same hash for same password and salt', async () => {
      const password = 'TestPassword123!';
      const salt = 'fixedSalt123';

      const result1 = await hashPassword(password, salt);
      const result2 = await hashPassword(password, salt);

      expect(result1.hash).toBe(result2.hash);
      expect(result1.salt).toBe(salt);
      expect(result2.salt).toBe(salt);
    });
  });

  describe('Password Strength Validation', () => {
    it('should validate various password strengths correctly', () => {
      const testCases = [
        {
          password: 'StrongPassword123!',
          expected: { isValid: true, strength: 'strong', score: 5 }
        },
        {
          password: 'MediumPass123',
          expected: { isValid: false, strength: 'strong', score: 4 }
        },
        {
          password: 'weakpass',
          expected: { isValid: false, strength: 'weak', score: 2 }
        },
        {
          password: '123',
          expected: { isValid: false, strength: 'weak', score: 1 }
        },
        {
          password: '',
          expected: { isValid: false, strength: 'weak', score: 0 }
        }
      ];

      testCases.forEach(({ password, expected }) => {
        const result = validatePasswordStrength(password);
        expect(result.isValid).toBe(expected.isValid);
        expect(result.strength).toBe(expected.strength);
        expect(result.score).toBe(expected.score);
      });
    });

    it('should validate all password requirements individually', () => {
      const result = validatePasswordStrength('TestPassword123!');

      expect(result.minLength).toBe(true);
      expect(result.hasUpperCase).toBe(true);
      expect(result.hasLowerCase).toBe(true);
      expect(result.hasNumbers).toBe(true);
      expect(result.hasSpecialChar).toBe(true);
    });
  });

  describe('Real-world Usage Scenarios', () => {
    it('should handle typical user registration flow', async () => {
      const email = '<EMAIL>';
      const password = 'UserPassword123!';
      const confirmPassword = 'UserPassword123!';

      // Validate password strength
      const strength = validatePasswordStrength(password);
      expect(strength.isValid).toBe(true);

      // Check passwords match
      expect(password).toBe(confirmPassword);

      // Encrypt for transmission (as backend expects)
      const encryptedPassword = await encryptPassword(password);
      const encryptedConfirmPassword = await encryptPassword(confirmPassword);

      expect(typeof encryptedPassword).toBe('string');
      expect(typeof encryptedConfirmPassword).toBe('string');
      expect(encryptedPassword).toContain(':');
      expect(encryptedConfirmPassword).toContain(':');
      expect(encryptedPassword.length).toBeGreaterThan(0);
      expect(encryptedConfirmPassword.length).toBeGreaterThan(0);

      // Verify they can be decrypted back to original
      const decrypted1 = await decryptPassword(encryptedPassword);
      const decrypted2 = await decryptPassword(encryptedConfirmPassword);
      expect(decrypted1).toBe(password);
      expect(decrypted2).toBe(confirmPassword);
    });

    it('should handle typical login flow', async () => {
      const password = 'LoginPassword123!';

      // Encrypt password for transmission (as would happen in login)
      const encryptedPassword = await encryptPassword(password);

      expect(typeof encryptedPassword).toBe('string');
      expect(encryptedPassword).toContain(':');
      expect(encryptedPassword.length).toBeGreaterThan(0);

      // Verify the original password is not exposed in plain text
      expect(encryptedPassword).not.toContain(password);
      expect(encryptedPassword).not.toContain('Login');
      expect(encryptedPassword).not.toContain('Password');
      expect(encryptedPassword).not.toContain('123');

      // But it should decrypt back to the original
      const decrypted = await decryptPassword(encryptedPassword);
      expect(decrypted).toBe(password);
    });

    it('should match backend expected format', async () => {
      const password = 'Pranay@123';

      const encrypted = await encryptPassword(password);

      // Should match the format: "base64IV:base64Ciphertext"
      const parts = encrypted.split(':');
      expect(parts).toHaveLength(2);

      const [ivPart, cipherPart] = parts;
      expect(ivPart.length).toBeGreaterThan(0);
      expect(cipherPart.length).toBeGreaterThan(0);

      // Should be valid base64
      expect(() => atob(ivPart)).not.toThrow();
      expect(() => atob(cipherPart)).not.toThrow();

      // Should decrypt back to original
      const decrypted = await decryptPassword(encrypted);
      expect(decrypted).toBe(password);
    });
  });

  describe('Security Properties', () => {
    it('should not expose original password in encrypted form', async () => {
      const password = 'SecretPassword123!';
      const encrypted = await encryptPassword(password);

      // The encrypted form should not contain the original password
      expect(encrypted).not.toContain(password);
      expect(encrypted).not.toContain('Secret');
      expect(encrypted).not.toContain('Password');
      expect(encrypted).not.toContain('123');

      // But should decrypt back to original
      const decrypted = await decryptPassword(encrypted);
      expect(decrypted).toBe(password);
    });

    it('should produce different outputs for similar passwords', async () => {
      const password1 = 'Password123!';
      const password2 = 'Password124!';

      const encrypted1 = await encryptPassword(password1);
      const encrypted2 = await encryptPassword(password2);

      expect(encrypted1).not.toBe(encrypted2);

      // But each should decrypt to its original
      const decrypted1 = await decryptPassword(encrypted1);
      const decrypted2 = await decryptPassword(encrypted2);
      expect(decrypted1).toBe(password1);
      expect(decrypted2).toBe(password2);
    });

    it('should use random IV for each encryption', async () => {
      const password = 'SamePassword123!';

      const encrypted1 = await encryptPassword(password);
      const encrypted2 = await encryptPassword(password);

      // Different encryptions should have different IVs
      const iv1 = encrypted1.split(':')[0];
      const iv2 = encrypted2.split(':')[0];
      expect(iv1).not.toBe(iv2);

      // But both should decrypt to the same password
      const decrypted1 = await decryptPassword(encrypted1);
      const decrypted2 = await decryptPassword(encrypted2);
      expect(decrypted1).toBe(password);
      expect(decrypted2).toBe(password);
    });
  });
});
