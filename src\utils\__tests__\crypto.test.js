import { describe, it, expect, beforeEach, vi } from 'vitest';
import {
  generateSalt,
  hashPassword,
  encryptPassword,
  decryptPassword,
  verifyPassword,
  generateSecureToken,
  validatePasswordStrength
} from '../crypto.js';

// Mock crypto.getRandomValues for consistent testing
const mockGetRandomValues = vi.fn();
const mockSubtle = {
  digest: vi.fn(),
  importKey: vi.fn(),
  encrypt: vi.fn(),
  decrypt: vi.fn()
};

beforeEach(() => {
  vi.clearAllMocks();

  // Mock the global crypto object using vi.stubGlobal
  vi.stubGlobal('crypto', {
    getRandomValues: mockGetRandomValues,
    subtle: mockSubtle
  });

  // Setup default mock behavior
  mockGetRandomValues.mockImplementation((array) => {
    // Fill with predictable values for testing
    for (let i = 0; i < array.length; i++) {
      array[i] = i % 256;
    }
    return array;
  });

  // Mock digest to return predictable hash
  const mockBuffer = new ArrayBuffer(32);
  const mockView = new Uint8Array(mockBuffer);
  for (let i = 0; i < mockView.length; i++) {
    mockView[i] = i % 256;
  }
  mockSubtle.digest.mockResolvedValue(mockBuffer);

  // Mock AES-GCM operations
  mockSubtle.importKey.mockResolvedValue('mock-crypto-key');

  // Mock encrypt to return predictable encrypted data
  const mockEncryptedBuffer = new ArrayBuffer(16);
  const mockEncryptedView = new Uint8Array(mockEncryptedBuffer);
  for (let i = 0; i < mockEncryptedView.length; i++) {
    mockEncryptedView[i] = (i + 100) % 256;
  }
  mockSubtle.encrypt.mockResolvedValue(mockEncryptedBuffer);

  // Mock decrypt to return predictable decrypted data
  const mockDecryptedText = 'decrypted-password';
  const mockDecryptedBuffer = new TextEncoder().encode(mockDecryptedText);
  mockSubtle.decrypt.mockResolvedValue(mockDecryptedBuffer);
});

describe('Crypto Utilities', () => {
  describe('generateSalt', () => {
    it('should generate salt with default length', () => {
      const salt = generateSalt();
      expect(salt).toBeDefined();
      expect(typeof salt).toBe('string');
      expect(mockGetRandomValues).toHaveBeenCalledWith(expect.any(Uint8Array));
    });

    it('should generate salt with custom length', () => {
      const customLength = 32;
      const salt = generateSalt(customLength);
      expect(salt).toBeDefined();
      expect(typeof salt).toBe('string');
      expect(mockGetRandomValues).toHaveBeenCalledWith(expect.any(Uint8Array));
    });
  });

  describe('hashPassword', () => {
    it('should hash password with provided salt', async () => {
      const password = 'testPassword123';
      const salt = 'testSalt';

      const result = await hashPassword(password, salt);

      expect(result).toHaveProperty('hash');
      expect(result).toHaveProperty('salt');
      expect(result.salt).toBe(salt);
      expect(typeof result.hash).toBe('string');
      expect(mockSubtle.digest).toHaveBeenCalled();
    });

    it('should generate salt if not provided', async () => {
      const password = 'testPassword123';

      const result = await hashPassword(password);

      expect(result).toHaveProperty('hash');
      expect(result).toHaveProperty('salt');
      expect(typeof result.salt).toBe('string');
      expect(mockGetRandomValues).toHaveBeenCalled();
    });

    it('should handle hashing errors', async () => {
      const password = 'testPassword123';
      mockSubtle.digest.mockRejectedValue(new Error('Hashing failed'));

      await expect(hashPassword(password)).rejects.toThrow('Failed to hash password');
    });
  });

  describe('encryptPassword', () => {
    it('should encrypt password using AES-GCM', async () => {
      const password = 'testPassword123';

      const encryptedPassword = await encryptPassword(password);

      expect(typeof encryptedPassword).toBe('string');
      expect(encryptedPassword).toContain(':'); // Should have IV:ciphertext format
      expect(encryptedPassword.length).toBeGreaterThan(0);
      expect(mockSubtle.digest).toHaveBeenCalled();
      expect(mockSubtle.importKey).toHaveBeenCalled();
      expect(mockSubtle.encrypt).toHaveBeenCalled();
    });

    it('should use custom encryption key', async () => {
      const password = 'testPassword123';
      const customKey = 'customkey123';

      const encryptedPassword = await encryptPassword(password, customKey);

      expect(typeof encryptedPassword).toBe('string');
      expect(encryptedPassword).toContain(':');
      expect(mockSubtle.digest).toHaveBeenCalled();
    });

    it('should handle encryption errors', async () => {
      const password = 'testPassword123';
      mockSubtle.encrypt.mockRejectedValue(new Error('Encryption failed'));

      await expect(encryptPassword(password)).rejects.toThrow('Failed to encrypt password');
    });
  });

  describe('decryptPassword', () => {
    it('should decrypt password using AES-GCM', async () => {
      const encryptedPassword = 'dGVzdGl2MTIzNDU2:dGVzdGNpcGhlcjEyMzQ1Ng=='; // Mock base64 data

      const decryptedPassword = await decryptPassword(encryptedPassword);

      expect(typeof decryptedPassword).toBe('string');
      expect(decryptedPassword).toBe('decrypted-password');
      expect(mockSubtle.digest).toHaveBeenCalled();
      expect(mockSubtle.importKey).toHaveBeenCalled();
      expect(mockSubtle.decrypt).toHaveBeenCalled();
    });

    it('should handle invalid encrypted password format', async () => {
      const invalidEncrypted = 'invalid-format';

      await expect(decryptPassword(invalidEncrypted)).rejects.toThrow('Failed to decrypt password');
    });

    it('should handle decryption errors', async () => {
      const encryptedPassword = 'dGVzdGl2MTIzNDU2:dGVzdGNpcGhlcjEyMzQ1Ng==';
      mockSubtle.decrypt.mockRejectedValue(new Error('Decryption failed'));

      await expect(decryptPassword(encryptedPassword)).rejects.toThrow('Failed to decrypt password');
    });
  });

  describe('verifyPassword', () => {
    it('should verify correct password', async () => {
      const password = 'testPassword123';
      const salt = 'testSalt';

      // First hash the password to get expected hash
      const { hash } = await hashPassword(password, salt);

      // Reset mock call count
      mockSubtle.digest.mockClear();

      // Verify the password
      const isValid = await verifyPassword(password, hash, salt);

      expect(isValid).toBe(true);
      expect(mockSubtle.digest).toHaveBeenCalled();
    });

    it('should reject incorrect password', async () => {
      const correctPassword = 'testPassword123';
      const incorrectPassword = 'wrongPassword';
      const salt = 'testSalt';

      // Mock different hash results for different inputs
      let callCount = 0;
      mockSubtle.digest.mockImplementation(() => {
        callCount++;
        const buffer = new ArrayBuffer(32);
        const view = new Uint8Array(buffer);
        // Return different hash for different calls
        for (let i = 0; i < view.length; i++) {
          view[i] = (i + callCount) % 256;
        }
        return Promise.resolve(buffer);
      });

      // Hash the correct password
      const { hash } = await hashPassword(correctPassword, salt);

      // Try to verify with incorrect password
      const isValid = await verifyPassword(incorrectPassword, hash, salt);

      expect(isValid).toBe(false);
    });

    it('should handle verification errors', async () => {
      const password = 'testPassword123';
      const hash = 'someHash';
      const salt = 'testSalt';

      mockSubtle.digest.mockRejectedValue(new Error('Verification failed'));

      const isValid = await verifyPassword(password, hash, salt);

      expect(isValid).toBe(false);
    });
  });

  describe('generateSecureToken', () => {
    it('should generate secure token with default length', () => {
      const token = generateSecureToken();
      expect(token).toBeDefined();
      expect(typeof token).toBe('string');
      expect(mockGetRandomValues).toHaveBeenCalledWith(expect.any(Uint8Array));
    });

    it('should generate secure token with custom length', () => {
      const customLength = 64;
      const token = generateSecureToken(customLength);
      expect(token).toBeDefined();
      expect(typeof token).toBe('string');
      expect(mockGetRandomValues).toHaveBeenCalledWith(expect.any(Uint8Array));
    });
  });

  describe('validatePasswordStrength', () => {
    it('should validate strong password', () => {
      const strongPassword = 'StrongPass123!';
      const result = validatePasswordStrength(strongPassword);

      expect(result.minLength).toBe(true);
      expect(result.hasUpperCase).toBe(true);
      expect(result.hasLowerCase).toBe(true);
      expect(result.hasNumbers).toBe(true);
      expect(result.hasSpecialChar).toBe(true);
      expect(result.isValid).toBe(true);
      expect(result.strength).toBe('strong');
      expect(result.score).toBe(5);
    });

    it('should validate medium password', () => {
      const mediumPassword = 'MediumPass123';
      const result = validatePasswordStrength(mediumPassword);

      expect(result.minLength).toBe(true);
      expect(result.hasUpperCase).toBe(true);
      expect(result.hasLowerCase).toBe(true);
      expect(result.hasNumbers).toBe(true);
      expect(result.hasSpecialChar).toBe(false);
      expect(result.isValid).toBe(false);
      expect(result.strength).toBe('strong'); // 4 criteria met = strong
      expect(result.score).toBe(4);
    });

    it('should validate weak password', () => {
      const weakPassword = 'weak';
      const result = validatePasswordStrength(weakPassword);

      expect(result.minLength).toBe(false);
      expect(result.hasUpperCase).toBe(false);
      expect(result.hasLowerCase).toBe(true);
      expect(result.hasNumbers).toBe(false);
      expect(result.hasSpecialChar).toBe(false);
      expect(result.isValid).toBe(false);
      expect(result.strength).toBe('weak');
      expect(result.score).toBe(1);
    });

    it('should handle empty password', () => {
      const result = validatePasswordStrength('');

      expect(result.minLength).toBe(false);
      expect(result.hasUpperCase).toBe(false);
      expect(result.hasLowerCase).toBe(false);
      expect(result.hasNumbers).toBe(false);
      expect(result.hasSpecialChar).toBe(false);
      expect(result.isValid).toBe(false);
      expect(result.strength).toBe('weak');
      expect(result.score).toBe(0);
    });
  });
});
